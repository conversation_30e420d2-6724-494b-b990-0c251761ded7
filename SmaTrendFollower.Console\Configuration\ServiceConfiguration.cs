using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Serilog;
using SmaTrendFollower.Services;
using SmaTrendFollower.Data;
using SmaTrendFollower.Examples;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Centralized service configuration to eliminate duplicate registrations and ensure consistency
/// </summary>
public static class ServiceConfiguration
{
    /// <summary>
    /// Register core infrastructure services (HTTP clients, factories, time providers)
    /// </summary>
    public static IServiceCollection AddCoreInfrastructure(this IServiceCollection services)
    {
        // HTTP client configuration
        services.AddHttpClient();
        
        // Configure enhanced HTTP clients with connection pooling and resilience
        var loggerFactory = LoggerFactory.Create(builder => builder.AddSerilog());
        var logger = loggerFactory.CreateLogger("HttpClientConfiguration");
        HttpClientConfigurationService.ConfigureHttpClients(services, logger);
        HttpClientConfigurationService.ConfigureConnectionPooling();

        // Rate limiting and client factories
        services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();
        services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
        services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
        
        // Time and session management
        services.AddSingleton<ITimeProvider, SystemTimeProvider>();
        services.AddSingleton<IMarketSessionGuard, MarketSessionGuard>();

        // Trading cycle management
        services.AddSingleton<ITradingCycleManager>(provider =>
        {
            var marketDataService = provider.GetRequiredService<IMarketDataService>();
            var marketSessionGuard = provider.GetRequiredService<IMarketSessionGuard>();
            var logger = provider.GetRequiredService<ILogger<TradingCycleManager>>();
            var configuration = provider.GetRequiredService<Microsoft.Extensions.Configuration.IConfiguration>();
            var config = TradingCycleConfig.FromConfiguration(configuration);
            return new TradingCycleManager(marketDataService, marketSessionGuard, logger, config);
        });

        // API health monitoring
        services.AddSingleton<IApiHealthMonitor, ApiHealthMonitor>();

        return services;
    }

    /// <summary>
    /// Register database contexts and caching services
    /// </summary>
    public static IServiceCollection AddDataServices(this IServiceCollection services)
    {
        // Database contexts
        services.AddDbContext<IndexCacheDbContext>(options =>
            options.UseSqlite("Data Source=index_cache.db"));
        services.AddDbContext<StockBarCacheDbContext>(options =>
            options.UseSqlite("Data Source=stock_cache.db"));

        // Cache services
        services.AddScoped<IIndexCacheService, IndexCacheService>();
        services.AddScoped<IStockBarCacheService, StockBarCacheService>();
        services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();

        // Optimized performance services
        services.AddScoped<IOptimizedBulkOperationsService, OptimizedBulkOperationsService>();
        services.AddScoped<IAdvancedCacheOptimizationService, AdvancedCacheOptimizationService>();

        // Redis and state management
        services.AddSingleton<IOptimizedRedisConnectionService, OptimizedRedisConnectionService>();
        services.AddSingleton<ILiveStateStore, LiveStateStore>();
        services.AddScoped<IBarStore, HistoricalBarStore>();
        services.AddScoped<IRedisWarmingService, RedisWarmingService>();

        return services;
    }

    /// <summary>
    /// Register market data and universe services
    /// </summary>
    public static IServiceCollection AddMarketDataServices(this IServiceCollection services)
    {
        // Market data services
        services.AddSingleton<IMarketDataService, MarketDataService>();
        services.AddSingleton<IStreamingDataService, StreamingDataService>();
        
        // Universe providers
        services.AddSingleton<IUniverseProvider, HybridUniverseProvider>();
        services.AddScoped<IDynamicUniverseProvider, DynamicUniverseProvider>();

        return services;
    }

    /// <summary>
    /// Register safety and risk management services
    /// </summary>
    public static IServiceCollection AddSafetyServices(this IServiceCollection services)
    {
        services.AddSingleton<ISafetyConfigurationService, SafetyConfigurationService>();
        services.AddScoped<IDynamicSafetyConfigurationService, DynamicSafetyConfigurationService>();
        services.AddSingleton<ITradingSafetyGuard, TradingSafetyGuard>();

        return services;
    }

    /// <summary>
    /// Register core trading strategy services
    /// </summary>
    public static IServiceCollection AddTradingServices(this IServiceCollection services)
    {
        // Signal generation and filtering
        services.AddScoped<IMomentumFilter, MomentumFilter>();
        services.AddScoped<IVolatilityFilter, VolatilityFilter>();
        services.AddScoped<IPositionSizer, DynamicPositionSizer>();
        services.AddScoped<ISignalGenerator, EnhancedSignalGenerator>();

        // Risk and portfolio management
        services.AddScoped<IRiskManager, RiskManager>();
        services.AddScoped<IPortfolioGate, PortfolioGate>();
        services.AddScoped<IStopManager, StopManager>();

        // Market regime detection
        services.AddScoped<IMarketRegimeService, MarketRegimeService>();

        return services;
    }

    /// <summary>
    /// Register enhanced trading services (volatility, options, notifications)
    /// </summary>
    public static IServiceCollection AddEnhancedTradingServices(this IServiceCollection services)
    {
        services.AddScoped<IVolatilityManager, VolatilityManager>();
        services.AddScoped<IOptionsStrategyManager, OptionsStrategyManager>();
        services.AddScoped<IDiscordNotificationService, DiscordNotificationService>();

        return services;
    }

    /// <summary>
    /// Register trade execution services with safety wrapper
    /// </summary>
    public static IServiceCollection AddTradeExecutionServices(this IServiceCollection services)
    {
        // Register trade executor with safety wrapper
        services.AddScoped<TradeExecutor>(); // Original executor
        services.AddScoped<ITradeExecutor>(provider =>
        {
            var innerExecutor = provider.GetRequiredService<TradeExecutor>();
            var safetyGuard = provider.GetRequiredService<ITradingSafetyGuard>();
            var logger = provider.GetRequiredService<ILogger<SafeTradeExecutor>>();
            return new SafeTradeExecutor(innerExecutor, safetyGuard, logger);
        });

        return services;
    }

    /// <summary>
    /// Register monitoring and observability services
    /// </summary>
    public static IServiceCollection AddMonitoringServices(this IServiceCollection services)
    {
        // Enhanced performance monitoring
        services.AddSingleton<PerformanceMonitoringService>();
        services.AddSingleton<IEnhancedPerformanceMonitoringService, EnhancedPerformanceMonitoringService>();
        services.AddScoped<AsyncBarFetchingService>();

        // Trading metrics and system health - Register as singletons that implement both interface and BackgroundService
        services.AddSingleton<TradingMetricsService>();
        services.AddSingleton<ITradingMetricsService>(provider => provider.GetRequiredService<TradingMetricsService>());
        services.AddHostedService<TradingMetricsService>(provider => provider.GetRequiredService<TradingMetricsService>());

        services.AddSingleton<SystemHealthService>();
        services.AddSingleton<ISystemHealthService>(provider => provider.GetRequiredService<SystemHealthService>());
        services.AddHostedService<SystemHealthService>(provider => provider.GetRequiredService<SystemHealthService>());

        // Real-time monitoring - Register as singletons for hosted services
        services.AddSingleton<RealTimeMarketMonitor>();
        services.AddSingleton<IRealTimeMarketMonitor>(provider => provider.GetRequiredService<RealTimeMarketMonitor>());
        services.AddHostedService<RealTimeMarketMonitor>(provider => provider.GetRequiredService<RealTimeMarketMonitor>());

        services.AddSingleton<LiveSignalIntelligence>();
        services.AddSingleton<ILiveSignalIntelligence>(provider => provider.GetRequiredService<LiveSignalIntelligence>());
        services.AddHostedService<LiveSignalIntelligence>(provider => provider.GetRequiredService<LiveSignalIntelligence>());

        // Trailing stop management - Register as singleton for hosted service
        services.AddSingleton<RealTimeTrailingStopManager>();
        services.AddSingleton<ITrailingStopManager>(provider => provider.GetRequiredService<RealTimeTrailingStopManager>());
        services.AddHostedService<RealTimeTrailingStopManager>(provider => provider.GetRequiredService<RealTimeTrailingStopManager>());

        // State management
        services.AddHostedService<StateFlushService>();

        return services;
    }

    /// <summary>
    /// Register adaptive optimization services for industrial-grade continuous learning
    /// </summary>
    public static IServiceCollection AddAdaptiveOptimizationServices(this IServiceCollection services)
    {
        // Core adaptive optimization services
        services.AddScoped<IPerformanceAnalysisService, PerformanceAnalysisService>();
        services.AddScoped<IAdaptiveLearningService, AdaptiveLearningService>();
        services.AddScoped<IStrategyOptimizationOrchestrator, StrategyOptimizationOrchestrator>();

        // Backtesting service (required for walk-forward analysis)
        services.AddScoped<IBacktestingEngine, BacktestingEngine>();

        return services;
    }



    /// <summary>
    /// Register all services for full trading system
    /// </summary>
    public static IServiceCollection AddFullTradingSystem(this IServiceCollection services)
    {
        return services
            .AddCoreInfrastructure()
            .AddDataServices()
            .AddMarketDataServices()
            .AddSafetyServices()
            .AddTradingServices()
            .AddEnhancedTradingServices()
            .AddTradeExecutionServices()
            .AddMonitoringServices()
            .AddAdaptiveOptimizationServices(); // 🚀 Industrial-grade adaptive optimization
    }

    /// <summary>
    /// Register minimal services for testing/examples
    /// </summary>
    public static IServiceCollection AddMinimalServices(this IServiceCollection services)
    {
        return services
            .AddCoreInfrastructure()
            .AddDataServices()
            .AddMarketDataServices();
    }

    /// <summary>
    /// Register services for cache operations only
    /// </summary>
    public static IServiceCollection AddCacheServices(this IServiceCollection services)
    {
        services.AddHttpClient();
        services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();
        
        // Database contexts
        services.AddDbContext<IndexCacheDbContext>(options =>
            options.UseSqlite("Data Source=index_cache.db"));
        services.AddDbContext<StockBarCacheDbContext>(options =>
            options.UseSqlite("Data Source=stock_cache.db"));

        // Cache services
        services.AddScoped<IIndexCacheService, IndexCacheService>();
        services.AddScoped<IStockBarCacheService, StockBarCacheService>();
        services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();

        return services;
    }

    /// <summary>
    /// Register services for signal generation testing
    /// </summary>
    public static IServiceCollection AddSignalTestingServices(this IServiceCollection services)
    {
        return services
            .AddMinimalServices()
            .AddTradingServices();
    }

    /// <summary>
    /// Register the trading service implementation (always uses enhanced version)
    /// </summary>
    public static IServiceCollection AddTradingServiceImplementation(this IServiceCollection services, bool useEnhanced = true)
    {
        // Always use enhanced trading service since basic version was removed in Phase 2 simplification
        services.AddScoped<ITradingService, EnhancedTradingService>();
        return services;
    }
}
