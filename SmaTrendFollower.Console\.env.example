#############################################
#  SmaTrendFollower – ENV TEMPLATE         #
#  Do NOT commit real keys.                #
#############################################

# ----- <PERSON><PERSON><PERSON> credentials -----
APCA_API_KEY_ID=REPLACE_ME
APCA_API_SECRET_KEY=REPLACE_ME
APCA_API_ENV=paper          # or live
# APCA_API_BASE_URL=https://paper-api.alpaca.markets  # optional

# ----- Polygon credentials -----
POLY_API_KEY=********************************

# ----- Bot settings ----------
BOT_TIMEZONE=America/New_York

# ----- Strategy settings -----
# Default universe size for screening
UNIVERSE_SIZE=500
# Top N symbols to trade (default 10)
TOP_N_SYMBOLS=10
# VIX threshold for position size throttling
VIX_THRESHOLD=25.0
# Enable options overlay strategies
ENABLE_OPTIONS_OVERLAY=true
# Enable protective puts
ENABLE_PROTECTIVE_PUTS=true
# Enable covered calls
ENABLE_COVERED_CALLS=true

# ----- Trading Cycle Intervals -----
# Default trading cycle interval in minutes (replaces hardcoded 30 minutes)
TRADING_CYCLE_INTERVAL_MINUTES=5
# High volatility cycle interval (when VIX > threshold)
TRADING_CYCLE_HIGH_VIX_MINUTES=2
# Normal volatility cycle interval
TRADING_CYCLE_NORMAL_VIX_MINUTES=5
# Low volatility cycle interval (when VIX < threshold)
TRADING_CYCLE_LOW_VIX_MINUTES=10
# Extended hours cycle interval (pre/post market)
TRADING_CYCLE_EXTENDED_HOURS_MINUTES=15
# Overnight cycle interval (market closed)
TRADING_CYCLE_OVERNIGHT_MINUTES=30
# VIX threshold for high volatility (above this = high vol interval)
TRADING_CYCLE_HIGH_VIX_THRESHOLD=25.0
# VIX threshold for low volatility (below this = low vol interval)
TRADING_CYCLE_LOW_VIX_THRESHOLD=15.0
# Enable dynamic VIX-based interval adjustment
TRADING_CYCLE_ENABLE_VIX_ADJUSTMENT=true
# Enable different intervals for extended hours
TRADING_CYCLE_ENABLE_EXTENDED_HOURS_ADJUSTMENT=true
# Minimum allowed cycle interval (safety limit)
TRADING_CYCLE_MIN_INTERVAL_MINUTES=1
# Maximum allowed cycle interval (safety limit)
TRADING_CYCLE_MAX_INTERVAL_MINUTES=60

# ----- Discord webhook (optional) -----
DISCORD_WEBHOOK_URL=REPLACE_ME

# ----- Redis configuration -----
# Redis connection string for cache warming and live trading state
REDIS_URL=localhost:6379
# Redis database number (default: 0)
REDIS_DATABASE=0
# Redis password (if required)
# REDIS_PASSWORD=your_redis_password

# ----- Risk management -----
# Maximum position size as percentage of account (default 10%)
MAX_POSITION_SIZE_PERCENT=0.10
# Maximum daily loss threshold (default $5000)
MAX_DAILY_LOSS=5000
# Minimum account equity for trading (default $25000 for PDT)
MIN_ACCOUNT_EQUITY=25000
